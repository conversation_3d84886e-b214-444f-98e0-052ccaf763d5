{"name": "cinextma", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "sb-start": "supabase start --debug --ignore-health-check", "sb-stop": "supabase stop --debug --all", "sb-restart": "supabase stop --debug --all && supabase start --debug --ignore-health-check", "sb-db-reset": "supabase db reset", "sb-db-types": "supabase gen types typescript --local > src/utils/supabase/types.ts"}, "dependencies": {"@bprogress/next": "^3.2.12", "@ducanh2912/next-pwa": "^10.2.9", "@heroui/react": "^2.8.3", "@hookform/resolvers": "^5.2.1", "@mantine/hooks": "^8.2.4", "@marsidev/react-turnstile": "^1.3.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.56.0", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.85.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "next": "15.5.2", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-share": "^5.2.2", "string-ts": "^2.2.1", "tailwindcss-motion": "^1.1.1", "tmdb-ts": "^2.0.2", "use-long-press": "^3.3.0", "vaul": "^1.1.2", "yet-another-react-lightbox": "^3.25.0", "zod": "^4.1.1"}, "devDependencies": {"@iconify/react": "^6.0.0", "@tailwindcss/postcss": "^4.1.12", "@tanstack/react-query-devtools": "^5.85.3", "@types/node": "^20", "@types/react": "19.1.12", "@types/react-dom": "19.1.9", "eslint": "^9.33.0", "eslint-config-next": "15.5.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "typescript": "^5", "webpack": "^5.101.2"}, "prettier": {"proseWrap": "never", "printWidth": 100, "plugins": ["prettier-plugin-tailwindcss"]}, "overrides": {"@types/react": "19.1.12", "@types/react-dom": "19.1.9"}}