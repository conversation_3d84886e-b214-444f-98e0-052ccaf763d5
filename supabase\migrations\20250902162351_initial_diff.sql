create table "public"."histories" (
    "user_id" uuid not null,
    "id" bigint generated by default as identity not null,
    "media_id" integer not null,
    "type" text not null,
    "season" integer not null default 0,
    "episode" integer not null default 0,
    "duration" double precision not null default 0,
    "last_position" double precision not null default 0,
    "completed" boolean not null default false,
    "adult" boolean not null,
    "backdrop_path" text,
    "poster_path" text,
    "release_date" date not null,
    "title" text not null,
    "vote_average" numeric(4,1) not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."histories" enable row level security;

create table "public"."profiles" (
    "id" uuid not null,
    "username" text not null,
    "created_at" timestamp with time zone default now()
);


alter table "public"."profiles" enable row level security;

create table "public"."watchlist" (
    "user_id" uuid not null,
    "id" integer not null,
    "type" text not null,
    "adult" boolean not null,
    "backdrop_path" text,
    "poster_path" text,
    "release_date" date not null,
    "title" text not null,
    "vote_average" numeric(4,1) not null,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."watchlist" enable row level security;

CREATE UNIQUE INDEX histories_pkey ON public.histories USING btree (id);

CREATE UNIQUE INDEX histories_user_id_media_id_type_season_episode_key ON public.histories USING btree (user_id, media_id, type, season, episode);

CREATE INDEX histories_user_updated_idx ON public.histories USING btree (user_id, updated_at DESC);

CREATE UNIQUE INDEX profiles_pkey ON public.profiles USING btree (id);

CREATE UNIQUE INDEX profiles_username_key ON public.profiles USING btree (username);

CREATE UNIQUE INDEX watchlist_pkey ON public.watchlist USING btree (user_id, id, type);

alter table "public"."histories" add constraint "histories_pkey" PRIMARY KEY using index "histories_pkey";

alter table "public"."profiles" add constraint "profiles_pkey" PRIMARY KEY using index "profiles_pkey";

alter table "public"."watchlist" add constraint "watchlist_pkey" PRIMARY KEY using index "watchlist_pkey";

alter table "public"."histories" add constraint "histories_type_check" CHECK ((type = ANY (ARRAY['movie'::text, 'tv'::text]))) not valid;

alter table "public"."histories" validate constraint "histories_type_check";

alter table "public"."histories" add constraint "histories_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."histories" validate constraint "histories_user_id_fkey";

alter table "public"."histories" add constraint "histories_user_id_media_id_type_season_episode_key" UNIQUE using index "histories_user_id_media_id_type_season_episode_key";

alter table "public"."profiles" add constraint "profiles_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."profiles" validate constraint "profiles_id_fkey";

alter table "public"."profiles" add constraint "profiles_username_key" UNIQUE using index "profiles_username_key";

alter table "public"."watchlist" add constraint "watchlist_type_check" CHECK ((type = ANY (ARRAY['movie'::text, 'tv'::text]))) not valid;

alter table "public"."watchlist" validate constraint "watchlist_type_check";

alter table "public"."watchlist" add constraint "watchlist_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."watchlist" validate constraint "watchlist_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.set_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
begin
  new.updated_at = now();
  return new;
end;
$function$
;

grant delete on table "public"."histories" to "anon";

grant insert on table "public"."histories" to "anon";

grant references on table "public"."histories" to "anon";

grant select on table "public"."histories" to "anon";

grant trigger on table "public"."histories" to "anon";

grant truncate on table "public"."histories" to "anon";

grant update on table "public"."histories" to "anon";

grant delete on table "public"."histories" to "authenticated";

grant insert on table "public"."histories" to "authenticated";

grant references on table "public"."histories" to "authenticated";

grant select on table "public"."histories" to "authenticated";

grant trigger on table "public"."histories" to "authenticated";

grant truncate on table "public"."histories" to "authenticated";

grant update on table "public"."histories" to "authenticated";

grant delete on table "public"."histories" to "service_role";

grant insert on table "public"."histories" to "service_role";

grant references on table "public"."histories" to "service_role";

grant select on table "public"."histories" to "service_role";

grant trigger on table "public"."histories" to "service_role";

grant truncate on table "public"."histories" to "service_role";

grant update on table "public"."histories" to "service_role";

grant delete on table "public"."profiles" to "anon";

grant insert on table "public"."profiles" to "anon";

grant references on table "public"."profiles" to "anon";

grant select on table "public"."profiles" to "anon";

grant trigger on table "public"."profiles" to "anon";

grant truncate on table "public"."profiles" to "anon";

grant update on table "public"."profiles" to "anon";

grant delete on table "public"."profiles" to "authenticated";

grant insert on table "public"."profiles" to "authenticated";

grant references on table "public"."profiles" to "authenticated";

grant select on table "public"."profiles" to "authenticated";

grant trigger on table "public"."profiles" to "authenticated";

grant truncate on table "public"."profiles" to "authenticated";

grant update on table "public"."profiles" to "authenticated";

grant delete on table "public"."profiles" to "service_role";

grant insert on table "public"."profiles" to "service_role";

grant references on table "public"."profiles" to "service_role";

grant select on table "public"."profiles" to "service_role";

grant trigger on table "public"."profiles" to "service_role";

grant truncate on table "public"."profiles" to "service_role";

grant update on table "public"."profiles" to "service_role";

grant delete on table "public"."watchlist" to "anon";

grant insert on table "public"."watchlist" to "anon";

grant references on table "public"."watchlist" to "anon";

grant select on table "public"."watchlist" to "anon";

grant trigger on table "public"."watchlist" to "anon";

grant truncate on table "public"."watchlist" to "anon";

grant update on table "public"."watchlist" to "anon";

grant delete on table "public"."watchlist" to "authenticated";

grant insert on table "public"."watchlist" to "authenticated";

grant references on table "public"."watchlist" to "authenticated";

grant select on table "public"."watchlist" to "authenticated";

grant trigger on table "public"."watchlist" to "authenticated";

grant truncate on table "public"."watchlist" to "authenticated";

grant update on table "public"."watchlist" to "authenticated";

grant delete on table "public"."watchlist" to "service_role";

grant insert on table "public"."watchlist" to "service_role";

grant references on table "public"."watchlist" to "service_role";

grant select on table "public"."watchlist" to "service_role";

grant trigger on table "public"."watchlist" to "service_role";

grant truncate on table "public"."watchlist" to "service_role";

grant update on table "public"."watchlist" to "service_role";

create policy "Users can delete their own histories"
on "public"."histories"
as permissive
for delete
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Users can insert their own histories"
on "public"."histories"
as permissive
for insert
to authenticated
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Users can update their own histories"
on "public"."histories"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Users can view their own histories"
on "public"."histories"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Enable read access for all users"
on "public"."profiles"
as permissive
for select
to anon, authenticated
using (true);


create policy "Users can insert their own profile"
on "public"."profiles"
as permissive
for insert
to authenticated
with check ((( SELECT auth.uid() AS uid) = id));


create policy "Users can update their own profile"
on "public"."profiles"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = id))
with check ((( SELECT auth.uid() AS uid) = id));


create policy "Users can delete their own watchlist"
on "public"."watchlist"
as permissive
for delete
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Users can insert their own watchlist"
on "public"."watchlist"
as permissive
for insert
to authenticated
with check ((( SELECT auth.uid() AS uid) = user_id));


create policy "Users can view their own watchlist"
on "public"."watchlist"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


CREATE TRIGGER set_updated_at BEFORE UPDATE ON public.histories FOR EACH ROW EXECUTE FUNCTION set_updated_at();


