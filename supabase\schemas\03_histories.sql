create table public.histories (
  user_id uuid not null references auth.users(id) on delete cascade,
  id bigint generated by default as identity primary key,
  media_id integer not null,
  type text not null check (type in ('movie','tv')),
  season integer not null default 0,
  episode integer not null default 0,
  duration double precision not null default 0,
  last_position double precision not null default 0,
  completed boolean not null default false,
  adult boolean not null,
  backdrop_path text,
  poster_path text,
  release_date date not null,
  title text not null,
  vote_average numeric(4,1) not null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  unique (user_id, media_id, type, season, episode)
);

-- Auto-update updated_at
create or replace function public.set_updated_at()
returns trigger
set search_path = ''
language plpgsql
as $$
begin
  new.updated_at = now();
  return new;
end;
$$;

create trigger set_updated_at
before update on public.histories
for each row
execute function public.set_updated_at();

-- Indexes
create index histories_user_updated_idx
on public.histories (user_id, updated_at desc);

-- Enable Row Level Security
alter table public.histories enable row level security;

-- Policies
create policy "Users can view their own histories"
on public.histories
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));

create policy "Users can insert their own histories"
on public.histories
for insert
to authenticated
with check ((( SELECT auth.uid() AS uid) = user_id));

create policy "Users can update their own histories"
on public.histories
for update
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id))
with check ((( SELECT auth.uid() AS uid) = user_id));

create policy "Users can delete their own histories"
on public.histories
for delete
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));