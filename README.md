# CINEXTMA - Free Movies & TV Shows Streaming

<div style="text-align:center">

![CINEXTMA Mockup Screenshot](https://raw.githubusercontent.com/wisnuwirayuda15/cinextma/refs/heads/master/src/public/img/mockup.png)

</div>

🍿CINEXTMA🍿 is an open-source, free movies and TV shows streaming platform built with the latest web technologies. It offers a seamless and enjoyable viewing experience, allowing users to discover, search, and enjoy a vast library of content.

## Key Features

- **💸 Free and Open-Source**: CINEXTMA is completely free to use and the source code is available for anyone to explore, contribute, and build upon.
- **🌗 Light and Dark Mode**: The application supports both light and dark modes, providing a customizable viewing experience that adapts to user preferences.
- **🧭 Discover Content**: Users can browse through various content categories, such as popular, trending, and upcoming, to discover new movies and TV shows to enjoy.
- **🔎 Powerful Search**: The search functionality allows users to easily find specific titles, actors, or genres, making it a breeze to locate the content they're interested in.
- **📂 Personal Library**: Users can build their own personalized library of favorite movies and TV shows, making it easy to keep track of what they've watched and want to watch.
- **💻📱 Responsive UI**: The platform's user interface is designed to be responsive and accessible across various devices, ensuring a consistent and enjoyable experience on desktops, tablets, and mobile devices.
- **📲 Progressive Web App**: The platform is designed to be installable on devices and provide native-like features, ensuring a seamless and engaging experience for users.
- **🙍‍♂️ User Account**: Users can create an account to access personalized features, such as a watchlist and history.

## Other Features

- **📺 TV Shows** (Finished)
- **🙍‍♂️ User Account** (Finished)
- **🏆 Achievements System** (Planned)
- **⚙️ Personal Settings** (Work in Progress)
- **🌐 Social Features** (Planned)

## Technologies Used

CINEXTMA is built using the following technologies:

- **Next.js 15 App Router**: The application leverages the latest version of Next.js, which includes the new App Router, providing a more intuitive and powerful development experience.
- **Tailwind CSS 4**: The user interface is styled using the Tailwind CSS utility-first CSS framework, ensuring a visually appealing and responsive design.
- **Next UI**: The project utilizes the Next UI library, which provides a set of high-quality, customizable React components that integrate seamlessly with Tailwind CSS.
- **TypeScript**: The codebase is written in TypeScript, ensuring better type safety, developer productivity, and maintainability.
- **TanStack Query**: The application uses the TanStack Query library for efficient data fetching and caching, providing a smooth and responsive user experience.
- **The Movie Database (TMDB) API**: CINEXTMA integrates with the TMDB API to retrieve movie and TV show data, ensuring access to a vast and up-to-date content library.
- **Supabase**: The project uses Supabase for user authentication and database management.

## Getting Started

To run CINEXTMA locally, follow these steps:

1. Clone the repository:

```
git clone https://github.com/wisnuwirayuda15/cinextma.git
```

2. Install the dependencies:

```
cd cinextma
npm install
```

3. Start the development server:

```
npm run dev
```

4. Open your browser and visit `http://localhost:3000` to access the CINEXTMA application.

## Contributing

Contributions to CINEXTMA are welcome! If you'd like to contribute, please refer to the [CONTRIBUTING](CONTRIBUTING.md) file for guidelines and instructions.

## License

CINEXTMA is licensed under the [MIT License](LICENSE). This means you are free to use, modify, and distribute the application, as long as you include the original copyright and license notice in your work.

## Star History

<a href="https://www.star-history.com/#wisnuwirayuda15/cinextma&Timeline">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=wisnuwirayuda15/cinextma&type=Timeline&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=wisnuwirayuda15/cinextma&type=Timeline" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=wisnuwirayuda15/cinextma&type=Timeline" />
 </picture>
</a>
