import { <PERSON><PERSON>illD<PERSON>like, AiFillLike } from "react-icons/ai";
import {
  FaAd,
  FaCalendarAlt,
  FaCloudSun,
  FaPlay,
  FaSearch,
  FaServer,
  FaSortAlphaDown,
  FaUser,
} from "react-icons/fa";
import {
  FaCheck,
  FaChevronLeft,
  FaChevronRight,
  FaCirclePlay,
  FaClock,
  FaExclamation,
  FaGear,
  FaInfo,
  FaStar,
  FaYoutube,
} from "react-icons/fa6";
import { FcGoogle } from "react-icons/fc";
import { FiLogOut } from "react-icons/fi";
import { HiArrowLongLeft, HiTrash } from "react-icons/hi2";
import { ImCross } from "react-icons/im";
import { IoIosClose, IoIosMail, IoIosRocket, IoIosShareAlt, IoMdHelpCircle } from "react-icons/io";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>o<PERSON>ye<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IoMenuOutline } from "react-icons/io5";
import { LuPlay } from "react-icons/lu";
import { MdContentCopy, MdMovie } from "react-icons/md";
import { PiTelevisionFill } from "react-icons/pi";
import { RiLockPasswordFill, RiRobot3Fill } from "react-icons/ri";
import { TbPlayerTrackNextFilled, TbPlayerTrackPrevFilled } from "react-icons/tb";
import { TiThList } from "react-icons/ti";

export {
  FaAd as Ads,
  HiArrowLongLeft as ArrowLeft,
  FaCalendarAlt as Calendar,
  FaCheck as Check,
  FaChevronLeft as ChevronLeft,
  FaChevronRight as ChevronRight,
  FaClock as Clock,
  IoIosClose as Close,
  MdContentCopy as Copy,
  ImCross as Cross,
  AiFillDislike as Dislike,
  IoEye as Eye,
  IoEyeOff as EyeOff,
  FaGear as Gear,
  FcGoogle as Google,
  IoGrid as Grid,
  IoMdHelpCircle as Help,
  FaInfo as Info,
  AiFillLike as Like,
  TiThList as List,
  RiLockPasswordFill as LockPassword,
  FiLogOut as Logout,
  IoIosMail as Mail,
  IoMenuOutline as Menu,
  MdMovie as Movie,
  TbPlayerTrackNextFilled as Next,
  FaCirclePlay as Play,
  FaPlay as PlayFilled,
  LuPlay as PlayOutline,
  TbPlayerTrackPrevFilled as Prev,
  RiRobot3Fill as Robot,
  IoIosRocket as Rocket,
  FaSearch as Search,
  FaCloudSun as Season,
  FaServer as Server,
  IoIosShareAlt as Share,
  FaSortAlphaDown as SortAlpha,
  FaStar as Star,
  HiTrash as Trash,
  PiTelevisionFill as TV,
  FaUser as User,
  FaExclamation as Warning,
  FaYoutube as Youtube,
};
